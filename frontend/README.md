# Comic Translation Frontend

A React + TypeScript + Vite application for comic translation management with authentication functionality.

## Tech Stack

- **React 19** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Styling
- **React Router DOM** - Routing
- **React Icons** - Icon library (Feather icons)
- **Supabase Auth UI** - Authentication components

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Dashboard/      # Dashboard-specific components
│   ├── Layouts/        # Layout components
│   └── Navbar/         # Navigation component
├── pages/              # Page components
├── routes/             # Route configuration
├── types/              # TypeScript type definitions
│   ├── dashboard.ts    # Dashboard-related types
│   └── user.ts         # User and auth-related types
├── utils/              # Utility functions
│   └── auth.ts         # Authentication utilities
└── main.tsx           # Application entry point
```

## Authentication System

The application includes a complete authentication system with login and registration functionality.

### Features

#### Login Form

- Email validation
- Password validation (minimum 6 characters)
- Show/hide password toggle
- Form validation with error messages
- Loading states during submission

#### Register Form

- Full name field
- Email validation
- Password validation (minimum 6 characters)
- Confirm password validation
- Show/hide password toggles for both password fields
- Form validation with error messages
- Loading states during submission

#### UI Components

- Built with Tailwind CSS for styling
- React Icons (Feather icons) for visual elements
- Responsive design
- Clean, modern interface
- Error handling with user-friendly messages

### Data Storage

Currently, authentication data is stored in localStorage (no API integration):

#### Stored Data

- `authUser`: JSON object containing user information
  ```json
  {
    "id": "timestamp_string",
    "email": "<EMAIL>",
    "fullName": "User Name",
    "createdAt": "ISO_date_string"
  }
  ```
- `isAuthenticated`: Boolean string ("true" or "false")

#### Auth Utilities

The `src/utils/auth.ts` file provides helper functions:

- `getCurrentUser()`: Get current authenticated user
- `isAuthenticated()`: Check if user is authenticated
- `setUser(user)`: Set authenticated user
- `clearAuth()`: Clear authentication data
- `getUserDisplayName()`: Get user display name

#### Type Definitions

The `src/types/user.ts` file contains all user and authentication related types:

- `LoginData`: Interface for login form data
- `RegisterData`: Interface for registration form data
- `AuthUser`: Interface for authenticated user data

### Navigation Integration

The navbar component includes:

- Login button (when not authenticated) - links to `/auth`
- Logout button (when authenticated) - clears auth data and refreshes page
- Dynamic display based on authentication status

### Routes

- `/auth` - Authentication page (login/register)
- Accessible from navbar login button
- Standalone page (not wrapped in main layout)

### Form Validation

#### Login Validation

- Email format validation
- Password minimum length (6 characters)

#### Register Validation

- Full name required
- Email format validation
- Password minimum length (6 characters)
- Password confirmation match

### Usage

1. Navigate to `/auth` or click "Login" in the navbar
2. Toggle between Login and Register forms
3. Fill in the required fields
4. Submit the form
5. User data will be stored in localStorage
6. Navbar will update to show logout option
7. Click logout to clear authentication data

### Future Enhancements

When integrating with a real backend:

1. Replace localStorage with actual API calls
2. Add proper session management
3. Implement JWT token handling
4. Add password reset functionality
5. Add email verification
6. Add social login options
7. Implement proper error handling for network requests

## Dependencies

### Core Dependencies

- `react`: ^19.1.0
- `react-dom`: ^19.1.0
- `react-router-dom`: ^7.3.0
- `tailwindcss`: ^4.1.10
- `react-icons`: ^5.5.0

### Authentication

- `@supabase/auth-ui-react`: ^0.4.7
- `@supabase/auth-ui-shared`: ^0.1.8

### Development

- `typescript`: ~5.8.3
- `vite`: ^6.3.5
- `@vitejs/plugin-react`: ^4.4.1
- `eslint`: ^9.25.0
