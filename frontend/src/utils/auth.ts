import type { AuthUser } from "../types/user";

export const AUTH_STORAGE_KEY = "authUser";
export const AUTH_STATUS_KEY = "isAuthenticated";

export const authUtils = {
  // Get current authenticated user
  getCurrentUser: (): AuthUser | null => {
    try {
      const userStr = localStorage.getItem(AUTH_STORAGE_KEY);
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error("Error parsing user data from localStorage:", error);
      return null;
    }
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return localStorage.getItem(AUTH_STATUS_KEY) === "true";
  },

  // Set authenticated user
  setUser: (user: AuthUser): void => {
    localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(user));
    localStorage.setItem(AUTH_STATUS_KEY, "true");
  },

  // Clear authentication data
  clearAuth: (): void => {
    localStorage.removeItem(AUTH_STORAGE_KEY);
    localStorage.removeItem(AUTH_STATUS_KEY);
  },

  // Get user display name
  getUserDisplayName: (): string => {
    const user = authUtils.getCurrentUser();
    return user?.fullName || user?.email || "User";
  },
};
