import { Link } from "react-router-dom";
import {
  FiGrid,
  FiFileText,
  FiUsers,
  FiMenu,
  FiChevronRight,
  FiLogIn,
  FiLogOut,
} from "react-icons/fi";
import { authUtils } from "../../utils/auth";

interface NavbarProps {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
}

export default function Navbar({ collapsed, setCollapsed }: NavbarProps) {
  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  const isAuthenticated = authUtils.isAuthenticated();
  const user = authUtils.getCurrentUser();

  const handleLogout = () => {
    authUtils.clearAuth();
    window.location.reload(); // Simple way to refresh the app state
  };

  return (
    <div
      className={`flex flex-col h-full bg-white border-r border-gray-200 overflow-hidden transition-all duration-300 ${
        collapsed ? "w-16" : "w-64"
      }`}
    >
      {/* Logo section */}
      <div className="h-24 p-4 border-b border-gray-200 flex justify-center items-center">
        <Link
          to="/"
          className="flex items-center justify-center gap-3 w-full h-full font-medium text-gray-800"
        >
          <FiFileText className="text-2xl" />
          {!collapsed && <span className="text-lg">ManhwaTrans</span>}
        </Link>
      </div>

      {/* Navigation links */}
      <div className="flex-1 p-2">
        <nav className="space-y-1">
          <Link
            to="/"
            className={`flex items-center ${
              collapsed ? "justify-center" : "justify-start"
            } gap-3 p-3 bg-gray-100 rounded-md text-gray-700`}
          >
            <FiGrid className="text-lg" />
            {!collapsed && <span>Dashboard</span>}
          </Link>
          <Link
            to="/series"
            className={`flex items-center ${
              collapsed ? "justify-center" : "justify-start"
            } gap-3 p-3 rounded-md text-gray-500 hover:bg-gray-100`}
          >
            <FiFileText className="text-lg" />
            {!collapsed && <span>Series</span>}
          </Link>
          <Link
            to="/users"
            className={`flex items-center ${
              collapsed ? "justify-center" : "justify-start"
            } gap-3 p-3 rounded-md text-gray-500 hover:bg-gray-100`}
          >
            <FiUsers className="text-lg" />
            {!collapsed && <span>Users</span>}
          </Link>
        </nav>
      </div>

      {/* Auth and Collapse buttons at bottom */}
      <div className="p-4 border-t border-gray-200 space-y-2">
        {/* Auth button */}
        {isAuthenticated ? (
          <button
            onClick={handleLogout}
            className={`flex items-center ${
              collapsed ? "justify-center" : "justify-start"
            } gap-3 w-full p-2 text-red-600 hover:bg-red-50 rounded-md`}
          >
            <FiLogOut className="text-lg" />
            {!collapsed && <span>Logout</span>}
          </button>
        ) : (
          <Link
            to="/auth"
            className={`flex items-center ${
              collapsed ? "justify-center" : "justify-start"
            } gap-3 w-full p-2 text-blue-600 hover:bg-blue-50 rounded-md`}
          >
            <FiLogIn className="text-lg" />
            {!collapsed && <span>Login</span>}
          </Link>
        )}

        {/* Collapse button */}
        <button
          onClick={toggleCollapse}
          className={`flex items-center ${
            collapsed ? "justify-center" : "justify-between"
          } w-full p-2 text-gray-600 hover:bg-gray-100 rounded-md`}
        >
          <div className="flex items-center gap-3">
            <FiMenu className="text-lg" />
            {!collapsed && <span>Collapse</span>}
          </div>
          {!collapsed && (
            <FiChevronRight
              className={`transform transition-transform ${
                collapsed ? "rotate-180" : ""
              }`}
            />
          )}
        </button>
      </div>
    </div>
  );
}
