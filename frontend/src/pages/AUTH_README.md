# Authentication Page

This document describes the Login/Register page implementation using Supabase UI library components.

## Features

### Login Form
- Email validation
- Password validation (minimum 6 characters)
- Show/hide password toggle
- Form validation with error messages
- Loading states during submission

### Register Form
- Full name field
- Email validation
- Password validation (minimum 6 characters)
- Confirm password validation
- Show/hide password toggles for both password fields
- Form validation with error messages
- Loading states during submission

### UI Components
- Built with Tailwind CSS for styling
- React Icons (Feather icons) for visual elements
- Responsive design
- Clean, modern interface
- Error handling with user-friendly messages

## Data Storage

Currently, the authentication data is stored in localStorage:

### Stored Data
- `authUser`: JSON object containing user information
  ```json
  {
    "id": "timestamp_string",
    "email": "<EMAIL>",
    "fullName": "User Name",
    "createdAt": "ISO_date_string"
  }
  ```
- `isAuthenticated`: Boolean string ("true" or "false")

### Auth Utilities
The `src/utils/auth.ts` file provides helper functions:
- `getCurrentUser()`: Get current authenticated user
- `isAuthenticated()`: Check if user is authenticated
- `setUser(user)`: Set authenticated user
- `clearAuth()`: Clear authentication data
- `getUserDisplayName()`: Get user display name

## Navigation Integration

The navbar component includes:
- Login button (when not authenticated) - links to `/auth`
- Logout button (when authenticated) - clears auth data and refreshes page
- Dynamic display based on authentication status

## Routes

- `/auth` - Authentication page (login/register)
- Accessible from navbar login button
- Standalone page (not wrapped in main layout)

## Form Validation

### Login Validation
- Email format validation
- Password minimum length (6 characters)

### Register Validation
- Full name required
- Email format validation
- Password minimum length (6 characters)
- Password confirmation match

## Future Enhancements

When integrating with a real backend:
1. Replace localStorage with actual API calls
2. Add proper session management
3. Implement JWT token handling
4. Add password reset functionality
5. Add email verification
6. Add social login options
7. Implement proper error handling for network requests

## Usage

1. Navigate to `/auth` or click "Login" in the navbar
2. Toggle between Login and Register forms
3. Fill in the required fields
4. Submit the form
5. User data will be stored in localStorage
6. Navbar will update to show logout option
7. Click logout to clear authentication data

## Dependencies

- `@supabase/auth-ui-react`: Supabase Auth UI components
- `@supabase/auth-ui-shared`: Shared Supabase UI utilities
- `react-icons/fi`: Feather icons
- `react-router-dom`: Routing
- `tailwindcss`: Styling
