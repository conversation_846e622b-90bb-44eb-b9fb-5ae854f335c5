// Dashboard related types

export interface StatsData {
  title: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
}

export interface ActivityItem {
  id: number;
  action: string;
  timestamp: string;
}

// Authentication related types
export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
}

export interface AuthUser {
  id: string;
  email: string;
  fullName: string;
  createdAt: string;
}
